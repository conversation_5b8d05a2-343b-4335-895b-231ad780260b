package com.dlo.ai.controller;

import com.dlo.ai.domain.AiDTO;
import com.dlo.ai.domain.AiVO;
import com.dlo.ai.service.AiService;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;

@RestController
@RequestMapping("/ai")
@RequiredArgsConstructor
public class ChatController {

//    private final ChatClient chatClient;
    private final AiService aiService;
    @PostMapping(produces = "text/event-stream;charset=utf-8")
    public Flux<AiVO> chat(@RequestBody AiDTO aiDTO) {
        return aiService.chat(aiDTO);
    }
    @GetMapping("/chat/history")
    public List<AiVO> getHistory() {
        return aiService.getHistory();
    }
    @GetMapping("/chat/clear")
    public void clearHistory() {
        aiService.clear();
    }

}
