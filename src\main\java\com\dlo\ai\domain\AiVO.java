package com.dlo.ai.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import reactor.core.publisher.Flux;

@Data
@NoArgsConstructor
@Slf4j
public class AiVO {
    private String content;
    private String role;
    public AiVO(Message message) {
        log.debug("message: {}", message);
        log.info("type:{}", message.getMessageType());
        switch (message.getMessageType()) {
            case USER:
                role = "user";
                break;
            case ASSISTANT:
                role = "assistant";
                break;
            default:
                role = "system";
                break;
        }
        this.content = message.getText();
    }
}
