package com.dlo.ai.service.impl;

import com.dlo.ai.domain.AiDTO;
import com.dlo.ai.domain.AiVO;
import com.dlo.ai.service.AiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class AiServiceImpl implements AiService {
    private static final long USER_ID = 1;
    private final ChatClient chatClient;
    private final ChatMemory chatMemory;
    /**
     * 保存
     * @param userId
     */
    @Override
    public void save(String userId) {

    }

    /**
     * 获取历史
     * @return
     */
    @Override
    public List<AiVO> getHistory() {
        List<Message> messages = chatMemory.get(String.valueOf(USER_ID));
        if (messages == null){
            return List.of();
        }
        return messages.stream().map(AiVO::new).toList();
    }

    /**
     * 聊天
     * @param aiDTO
     * @return
     */
    @Override
    public Flux<AiVO> chat(AiDTO aiDTO) {
        Flux<String> content = chatClient.prompt()
                .user(aiDTO.getContent())
                .advisors(advisorSpec -> advisorSpec.param(ChatMemory.CONVERSATION_ID, USER_ID))
                .toolContext(Map.of("user_id", USER_ID))
                .stream()
                .content();
       return content.map(c-> {
           AiVO aiVO = new AiVO();
           aiVO.setContent(c);
           aiVO.setRole("assistant");
           return aiVO;
       });
    }

    @Override
    public void clear() {
        chatMemory.clear(String.valueOf(USER_ID));
    }
}
