package com.dlo.ai.tool;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class AiTool {
    @Tool(description = "获取当前用户id")
    public List<ToolVo> getUserInfo(ToolContext toolContext) {
        Object userId = toolContext.getContext().get("user_id");
        ToolVo toolVo = new ToolVo();
        toolVo.setId(Long.parseLong(userId.toString()));
        log.info("用户id：{}", userId);
        return List.of(toolVo);
    }

    @Data
    public static class ToolVo{
        private Long id;
    }
}
